version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./farmland.db
      - SECRET_KEY=your-secret-key-here
      - DEBUG=true
    volumes:
      - ./data:/app/data
      - ./static:/app/static
    restart: unless-stopped

  # PostgreSQL数据库（可选，生产环境使用）
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=farmland_db
      - POSTGRES_USER=farmland_user
      - POSTGRES_PASSWORD=farmland_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data:
