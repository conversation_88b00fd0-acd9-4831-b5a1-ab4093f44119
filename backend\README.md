# 数字农田监测系统后端

基于 FastAPI 的数字农田监测系统后端服务，提供农田监测数据管理和设备管理功能。

## 功能特性

- 🌾 **农田监测数据管理** - 土壤温度、湿度、pH值、环境数据等
- 📱 **设备管理** - 监测设备的位置、状态、电量等信息管理
- 👤 **用户认证** - JWT Token 认证系统
- 🎛️ **管理后台** - 基于Web的管理界面
- 📚 **API文档** - 自动生成的Swagger文档
- 🔒 **权限控制** - 管理员和普通用户权限分离

## 技术栈

- **后端框架**: FastAPI
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: SQLAlchemy
- **认证**: JWT Token
- **模板引擎**: Jinja2
- **API文档**: Swagger/OpenAPI

## 快速开始

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 初始化数据库和示例数据

```bash
python -m app.init_data
```

### 3. 启动服务

```bash
python run.py
```

或者使用 uvicorn 直接启动：

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问服务

- **管理后台**: http://localhost:8000/admin
- **API文档**: http://localhost:8000/docs
- **API根路径**: http://localhost:8000/api/v1

### 默认管理员账号

- 用户名: `admin`
- 密码: `admin123`

## API 接口

### 认证接口
- `POST /api/v1/auth/login` - 用户登录

### 用户管理
- `GET /api/v1/users/me` - 获取当前用户信息
- `GET /api/v1/users/` - 获取用户列表（管理员）
- `POST /api/v1/users/` - 创建用户（管理员）
- `PUT /api/v1/users/{user_id}` - 更新用户（管理员）
- `DELETE /api/v1/users/{user_id}` - 删除用户（管理员）

### 设备管理
- `GET /api/v1/equipment/` - 获取设备列表
- `GET /api/v1/equipment/{equipment_id}` - 获取设备详情
- `POST /api/v1/equipment/` - 创建设备（管理员）
- `PUT /api/v1/equipment/{equipment_id}` - 更新设备（管理员）
- `DELETE /api/v1/equipment/{equipment_id}` - 删除设备（管理员）

### 农田监测数据
- `GET /api/v1/farmland-data/` - 获取监测数据列表
- `GET /api/v1/farmland-data/latest/{equipment_id}` - 获取设备最新数据
- `GET /api/v1/farmland-data/{data_id}` - 获取数据详情
- `POST /api/v1/farmland-data/` - 创建监测数据
- `PUT /api/v1/farmland-data/{data_id}` - 更新监测数据（管理员）
- `DELETE /api/v1/farmland-data/{data_id}` - 删除监测数据（管理员）

## 数据模型

### 设备模型 (Equipment)
```json
{
  "id": 1,
  "name": "农田监测设备1",
  "equipment_id": "FARM_001",
  "longitude": 116.3974,
  "latitude": 39.9093,
  "altitude": 0.0,
  "state": "运行中",
  "is_active": true,
  "model": "FM-2000",
  "manufacturer": "农业科技有限公司"
}
```

### 农田监测数据模型 (FarmlandMonitoringData)
```json
{
  "id": 1,
  "equipment_id": 1,
  "soil_temperature": 22.5,
  "soil_moisture": 65.2,
  "soil_ph": 6.8,
  "ambient_temperature": 25.3,
  "ambient_humidity": 72.1,
  "light_intensity": 45000,
  "monitor_battery": 85.5,
  "recorded_at": "2024-01-01T12:00:00",
  "notes": "正常监测数据"
}
```

## 配置说明

主要配置项在 `app/config.py` 中：

- `database_url`: 数据库连接URL
- `secret_key`: JWT密钥
- `admin_username/admin_password`: 默认管理员账号
- `allowed_origins`: CORS允许的源

## 部署说明

### 生产环境部署

1. 修改配置文件，使用PostgreSQL数据库
2. 设置环境变量或创建 `.env` 文件
3. 使用 Gunicorn 或其他WSGI服务器部署

```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker 部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "run.py"]
```

## 开发说明

### 项目结构
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # 应用入口
│   ├── config.py            # 配置文件
│   ├── database.py          # 数据库配置
│   ├── models/              # 数据模型
│   ├── schemas/             # Pydantic模型
│   ├── api/                 # API路由
│   ├── crud/                # 数据库操作
│   ├── auth/                # 认证相关
│   └── admin/               # 后台管理
├── requirements.txt         # 依赖包
└── README.md               # 说明文档
```

### 添加新功能

1. 在 `models/` 中定义数据模型
2. 在 `schemas/` 中定义Pydantic模型
3. 在 `crud/` 中实现数据库操作
4. 在 `api/` 中创建API路由
5. 在 `admin/` 中添加管理界面（可选）

## 许可证

MIT License
