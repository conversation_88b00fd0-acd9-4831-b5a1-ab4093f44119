from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class EquipmentBase(BaseModel):
    """设备基础模型"""
    name: str
    equipment_id: str
    longitude: float
    latitude: float
    altitude: float = 0.0
    state: str = "运行中"
    is_active: bool = True
    model: Optional[str] = None
    manufacturer: Optional[str] = None


class EquipmentCreate(EquipmentBase):
    """创建设备模型"""
    install_date: Optional[datetime] = None


class EquipmentUpdate(BaseModel):
    """更新设备模型"""
    name: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None
    altitude: Optional[float] = None
    state: Optional[str] = None
    is_active: Optional[bool] = None
    model: Optional[str] = None
    manufacturer: Optional[str] = None
    install_date: Optional[datetime] = None


class Equipment(EquipmentBase):
    """返回给客户端的设备模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
