{% extends "base.html" %}

{% block title %}监测数据 - 数字农田监测系统管理后台{% endblock %}

{% block header %}农田监测数据{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h4>监测数据列表</h4>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDataModal">
        <i class="bi bi-plus"></i> 添加数据
    </button>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>设备ID</th>
                        <th>土壤温度(℃)</th>
                        <th>土壤湿度(%)</th>
                        <th>土壤pH</th>
                        <th>环境温度(℃)</th>
                        <th>环境湿度(%)</th>
                        <th>光照强度(lux)</th>
                        <th>电量(%)</th>
                        <th>记录时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for data in data_list %}
                    <tr>
                        <td>{{ data.id }}</td>
                        <td>{{ data.equipment_id }}</td>
                        <td>{{ "%.1f"|format(data.soil_temperature) }}</td>
                        <td>{{ "%.1f"|format(data.soil_moisture) }}</td>
                        <td>{{ "%.1f"|format(data.soil_ph) }}</td>
                        <td>{{ "%.1f"|format(data.ambient_temperature) }}</td>
                        <td>{{ "%.1f"|format(data.ambient_humidity) }}</td>
                        <td>{{ "%.0f"|format(data.light_intensity) }}</td>
                        <td>
                            {% if data.monitor_battery > 50 %}
                                <span class="badge bg-success">{{ "%.0f"|format(data.monitor_battery) }}%</span>
                            {% elif data.monitor_battery > 20 %}
                                <span class="badge bg-warning">{{ "%.0f"|format(data.monitor_battery) }}%</span>
                            {% else %}
                                <span class="badge bg-danger">{{ "%.0f"|format(data.monitor_battery) }}%</span>
                            {% endif %}
                        </td>
                        <td>{{ data.recorded_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editData({{ data.id }})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteData({{ data.id }})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加数据模态框 -->
<div class="modal fade" id="addDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加监测数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDataForm">
                    <div class="mb-3">
                        <label for="equipment_id" class="form-label">设备ID</label>
                        <input type="number" class="form-control" id="equipment_id" name="equipment_id" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="soil_temperature" class="form-label">土壤温度(℃)</label>
                                <input type="number" step="0.1" class="form-control" id="soil_temperature" name="soil_temperature" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="soil_moisture" class="form-label">土壤湿度(%)</label>
                                <input type="number" step="0.1" class="form-control" id="soil_moisture" name="soil_moisture" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="soil_ph" class="form-label">土壤pH值</label>
                                <input type="number" step="0.1" class="form-control" id="soil_ph" name="soil_ph" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ambient_temperature" class="form-label">环境温度(℃)</label>
                                <input type="number" step="0.1" class="form-control" id="ambient_temperature" name="ambient_temperature" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ambient_humidity" class="form-label">环境湿度(%)</label>
                                <input type="number" step="0.1" class="form-control" id="ambient_humidity" name="ambient_humidity" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="light_intensity" class="form-label">光照强度(lux)</label>
                                <input type="number" step="1" class="form-control" id="light_intensity" name="light_intensity" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="monitor_battery" class="form-label">设备电量(%)</label>
                        <input type="number" step="0.1" class="form-control" id="monitor_battery" name="monitor_battery" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitData()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function submitData() {
    const form = document.getElementById('addDataForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 转换数值类型
    data.equipment_id = parseInt(data.equipment_id);
    data.soil_temperature = parseFloat(data.soil_temperature);
    data.soil_moisture = parseFloat(data.soil_moisture);
    data.soil_ph = parseFloat(data.soil_ph);
    data.ambient_temperature = parseFloat(data.ambient_temperature);
    data.ambient_humidity = parseFloat(data.ambient_humidity);
    data.light_intensity = parseFloat(data.light_intensity);
    data.monitor_battery = parseFloat(data.monitor_battery);
    
    fetch('/api/v1/farmland-data/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            alert('数据添加成功！');
            location.reload();
        } else {
            alert('添加失败：' + (data.detail || '未知错误'));
        }
    })
    .catch(error => {
        alert('添加失败：' + error.message);
    });
}

function editData(id) {
    alert('编辑功能待实现');
}

function deleteData(id) {
    if (confirm('确定要删除这条数据吗？')) {
        fetch(`/api/v1/farmland-data/${id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                alert('数据删除成功！');
                location.reload();
            } else {
                alert('删除失败');
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}
</script>
{% endblock %}
