"""
为前端提供特殊格式数据的API端点
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..crud.equipment import equipment_crud
from ..crud.farmland_data import farmland_data_crud
from ..auth.dependencies import get_current_active_user
from ..models.user import User

router = APIRouter()


@router.get("/equipment-geojson")
async def get_equipment_geojson(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取设备的GeoJSON格式数据，兼容前端Cesium显示"""
    equipment_list = equipment_crud.get_active_equipment(db, limit=1000)
    
    features = []
    for equipment in equipment_list:
        feature = {
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [equipment.longitude, equipment.latitude, equipment.altitude]
            },
            "properties": {
                "id": equipment.id,
                "name": equipment.name,
                "equipment_id": equipment.equipment_id,
                "state": equipment.state,
                "model": equipment.model,
                "manufacturer": equipment.manufacturer
            }
        }
        features.append(feature)
    
    geojson = {
        "type": "FeatureCollection",
        "features": features
    }
    
    return geojson


@router.get("/equipment-monitoring-data/{equipment_id}")
async def get_equipment_monitoring_data(
    equipment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取设备的最新监测数据，格式兼容前端显示"""
    # 获取设备信息
    equipment = equipment_crud.get(db, id=equipment_id)
    if not equipment:
        return {"error": "Equipment not found"}
    
    # 获取最新监测数据
    latest_data = farmland_data_crud.get_latest_by_equipment(db, equipment_id=equipment_id)
    
    if not latest_data:
        # 如果没有数据，返回默认值
        return {
            "soilTemperature": 22.0,
            "soilMoisture": 60.0,
            "soilPH": 6.5,
            "ambientTemperature": 25.0,
            "ambientHumidity": 70.0,
            "monitorBattery": 80.0,
            "lightIntensity": 50000.0,
            "equipmentName": equipment.name,
            "equipment": {
                "id": str(equipment.id),
                "name": equipment.name,
                "pos": {
                    "x": equipment.longitude,
                    "y": equipment.latitude,
                    "z": equipment.altitude
                },
                "state": equipment.state
            }
        }
    
    # 返回实际数据，字段名与前端TypeScript接口匹配
    return {
        "soilTemperature": latest_data.soil_temperature,
        "soilMoisture": latest_data.soil_moisture,
        "soilPH": latest_data.soil_ph,
        "ambientTemperature": latest_data.ambient_temperature,
        "ambientHumidity": latest_data.ambient_humidity,
        "monitorBattery": latest_data.monitor_battery,
        "lightIntensity": latest_data.light_intensity,
        "equipmentName": equipment.name,
        "equipment": {
            "id": str(equipment.id),
            "name": equipment.name,
            "pos": {
                "x": equipment.longitude,
                "y": equipment.latitude,
                "z": equipment.altitude
            },
            "state": equipment.state
        },
        "recordedAt": latest_data.recorded_at.isoformat(),
        "notes": latest_data.notes
    }


@router.get("/dashboard-stats")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取仪表板统计数据"""
    equipment_count = len(equipment_crud.get_multi(db, limit=1000))
    active_equipment_count = len(equipment_crud.get_active_equipment(db, limit=1000))
    recent_data_count = len(farmland_data_crud.get_recent_data(db, hours=24, limit=1000))
    
    # 获取各状态设备数量
    running_count = len(equipment_crud.get_by_state(db, state="运行中", limit=1000))
    offline_count = len(equipment_crud.get_by_state(db, state="离线", limit=1000))
    fault_count = len(equipment_crud.get_by_state(db, state="故障", limit=1000))
    
    return {
        "equipment_count": equipment_count,
        "active_equipment_count": active_equipment_count,
        "recent_data_count": recent_data_count,
        "equipment_status": {
            "running": running_count,
            "offline": offline_count,
            "fault": fault_count
        }
    }


@router.get("/equipment-list-simple")
async def get_equipment_list_simple(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取简化的设备列表，用于前端下拉选择等"""
    equipment_list = equipment_crud.get_active_equipment(db, limit=1000)
    
    return [
        {
            "id": equipment.id,
            "name": equipment.name,
            "equipment_id": equipment.equipment_id,
            "state": equipment.state
        }
        for equipment in equipment_list
    ]
