#!/usr/bin/env python3
"""
启动脚本
"""
import uvicorn
from app.main import app
from app.config import settings

if __name__ == "__main__":
    print(f"Starting {settings.app_name} v{settings.app_version}")
    print(f"Admin URL: http://localhost:8000/admin")
    print(f"API Docs: http://localhost:8000/docs")
    print(f"Default admin: {settings.admin_username} / {settings.admin_password}")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )
