from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # 应用配置
    app_name: str = "Digital Farmland Backend"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 数据库配置
    database_url: str = "sqlite:///./farmland.db"
    
    # JWT配置
    secret_key: str = "your-secret-key-here-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS配置
    allowed_origins: list = ["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"]
    
    # 管理员配置
    admin_username: str = "admin"
    admin_password: str = "admin123"
    admin_email: str = "<EMAIL>"
    
    class Config:
        env_file = ".env"


settings = Settings()
