from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..crud.farmland_data import farmland_data_crud
from ..crud.equipment import equipment_crud
from ..schemas.farmland_data import FarmlandMonitoringData, FarmlandMonitoringDataCreate, FarmlandMonitoringDataUpdate
from ..auth.dependencies import get_current_active_user, get_current_admin_user
from ..models.user import User

router = APIRouter()


@router.get("/", response_model=List[FarmlandMonitoringData])
async def read_farmland_data(
    skip: int = 0,
    limit: int = 100,
    equipment_id: Optional[int] = Query(None, description="设备ID"),
    hours: Optional[int] = Query(None, description="获取最近几小时的数据"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取农田监测数据"""
    if hours:
        data = farmland_data_crud.get_recent_data(db, hours=hours, skip=skip, limit=limit)
    elif start_date or end_date:
        data = farmland_data_crud.get_by_date_range(
            db, 
            equipment_id=equipment_id,
            start_date=start_date,
            end_date=end_date,
            skip=skip,
            limit=limit
        )
    elif equipment_id:
        data = farmland_data_crud.get_by_equipment(db, equipment_id=equipment_id, skip=skip, limit=limit)
    else:
        data = farmland_data_crud.get_multi(db, skip=skip, limit=limit)
    return data


@router.get("/latest/{equipment_id}", response_model=FarmlandMonitoringData)
async def read_latest_data_by_equipment(
    equipment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取设备最新的监测数据"""
    data = farmland_data_crud.get_latest_by_equipment(db, equipment_id=equipment_id)
    if data is None:
        raise HTTPException(status_code=404, detail="No data found for this equipment")
    return data


@router.get("/{data_id}", response_model=FarmlandMonitoringData)
async def read_farmland_data_by_id(
    data_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """根据ID获取监测数据"""
    data = farmland_data_crud.get(db, id=data_id)
    if data is None:
        raise HTTPException(status_code=404, detail="Data not found")
    return data


@router.post("/", response_model=FarmlandMonitoringData)
async def create_farmland_data(
    data_in: FarmlandMonitoringDataCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建农田监测数据"""
    # 验证设备是否存在
    equipment = equipment_crud.get(db, id=data_in.equipment_id)
    if equipment is None:
        raise HTTPException(status_code=404, detail="Equipment not found")
    
    data = farmland_data_crud.create(db, obj_in=data_in)
    return data


@router.put("/{data_id}", response_model=FarmlandMonitoringData)
async def update_farmland_data(
    data_id: int,
    data_in: FarmlandMonitoringDataUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新农田监测数据（仅管理员）"""
    data = farmland_data_crud.get(db, id=data_id)
    if data is None:
        raise HTTPException(status_code=404, detail="Data not found")
    data = farmland_data_crud.update(db, db_obj=data, obj_in=data_in)
    return data


@router.delete("/{data_id}")
async def delete_farmland_data(
    data_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """删除农田监测数据（仅管理员）"""
    data = farmland_data_crud.get(db, id=data_id)
    if data is None:
        raise HTTPException(status_code=404, detail="Data not found")
    data = farmland_data_crud.remove(db, id=data_id)
    return {"message": "Data deleted successfully"}
