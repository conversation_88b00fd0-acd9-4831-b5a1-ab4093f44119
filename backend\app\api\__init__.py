from fastapi import APIRouter
from .auth import router as auth_router
from .equipment import router as equipment_router
from .farmland_data import router as farmland_data_router
from .users import router as users_router

api_router = APIRouter()

api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(users_router, prefix="/users", tags=["用户管理"])
api_router.include_router(equipment_router, prefix="/equipment", tags=["设备管理"])
api_router.include_router(farmland_data_router, prefix="/farmland-data", tags=["农田监测数据"])
