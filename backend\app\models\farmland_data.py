from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..database import Base


class FarmlandMonitoringData(Base):
    """农田监测数据模型"""
    __tablename__ = "farmland_monitoring_data"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 关联设备
    equipment_id = Column(Integer, ForeignKey("equipment.id"), nullable=False)
    equipment = relationship("Equipment", backref="monitoring_data")
    
    # 土壤数据
    soil_temperature = Column(Float, nullable=False, comment="土壤温度(℃)")
    soil_moisture = Column(Float, nullable=False, comment="土壤湿度(%)")
    soil_ph = Column(Float, nullable=False, comment="土壤酸碱度")
    
    # 环境数据
    ambient_temperature = Column(Float, nullable=False, comment="环境温度(℃)")
    ambient_humidity = Column(Float, nullable=False, comment="环境湿度(%)")
    light_intensity = Column(Float, nullable=False, comment="光照强度(lux)")
    
    # 设备状态
    monitor_battery = Column(Float, nullable=False, comment="监测器电量(%)")
    
    # 时间戳
    recorded_at = Column(DateTime(timezone=True), server_default=func.now(), comment="记录时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 可选的备注信息
    notes = Column(String(500), comment="备注信息")
