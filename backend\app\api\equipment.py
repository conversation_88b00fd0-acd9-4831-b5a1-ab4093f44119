from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..crud.equipment import equipment_crud
from ..schemas.equipment import Equipment, EquipmentCreate, EquipmentUpdate
from ..auth.dependencies import get_current_active_user, get_current_admin_user
from ..models.user import User

router = APIRouter()


@router.get("/", response_model=List[Equipment])
async def read_equipment(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = Query(False, description="只获取活跃设备"),
    state: str = Query(None, description="按状态筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取设备列表"""
    if state:
        equipment = equipment_crud.get_by_state(db, state=state, skip=skip, limit=limit)
    elif active_only:
        equipment = equipment_crud.get_active_equipment(db, skip=skip, limit=limit)
    else:
        equipment = equipment_crud.get_multi(db, skip=skip, limit=limit)
    return equipment


@router.get("/{equipment_id}", response_model=Equipment)
async def read_equipment_by_id(
    equipment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """根据ID获取设备"""
    equipment = equipment_crud.get(db, id=equipment_id)
    if equipment is None:
        raise HTTPException(status_code=404, detail="Equipment not found")
    return equipment


@router.post("/", response_model=Equipment)
async def create_equipment(
    equipment_in: EquipmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """创建设备（仅管理员）"""
    # 检查设备ID是否已存在
    existing_equipment = equipment_crud.get_by_equipment_id(db, equipment_id=equipment_in.equipment_id)
    if existing_equipment:
        raise HTTPException(
            status_code=400,
            detail="Equipment with this ID already exists"
        )
    equipment = equipment_crud.create(db, obj_in=equipment_in)
    return equipment


@router.put("/{equipment_id}", response_model=Equipment)
async def update_equipment(
    equipment_id: int,
    equipment_in: EquipmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新设备（仅管理员）"""
    equipment = equipment_crud.get(db, id=equipment_id)
    if equipment is None:
        raise HTTPException(status_code=404, detail="Equipment not found")
    equipment = equipment_crud.update(db, db_obj=equipment, obj_in=equipment_in)
    return equipment


@router.delete("/{equipment_id}")
async def delete_equipment(
    equipment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """删除设备（仅管理员）"""
    equipment = equipment_crud.get(db, id=equipment_id)
    if equipment is None:
        raise HTTPException(status_code=404, detail="Equipment not found")
    equipment = equipment_crud.remove(db, id=equipment_id)
    return {"message": "Equipment deleted successfully"}
