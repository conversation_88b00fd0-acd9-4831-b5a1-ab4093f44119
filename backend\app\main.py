from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session

from .config import settings
from .database import create_tables, get_db
from .api import api_router
from .crud.user import user_crud
from .schemas.user import UserCreate
from .admin.admin import setup_admin

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="数字农田监测系统后端API",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix="/api/v1")

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    # 创建数据库表
    create_tables()
    
    # 创建默认管理员用户
    db = next(get_db())
    admin_user = user_crud.get_by_username(db, username=settings.admin_username)
    if not admin_user:
        admin_user_in = UserCreate(
            username=settings.admin_username,
            email=settings.admin_email,
            password=settings.admin_password,
            is_admin=True
        )
        user_crud.create(db, obj_in=admin_user_in)
        print(f"Created admin user: {settings.admin_username}")
    
    # 设置管理后台
    setup_admin(app)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用数字农田监测系统API",
        "version": settings.app_version,
        "docs": "/docs",
        "admin": "/admin"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
