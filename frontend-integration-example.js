/**
 * 前端集成示例 - 如何在Vue3项目中调用后端API
 * 
 * 这个文件展示了如何在您的前端项目中集成后端API
 * 请将这些代码片段集成到您的Vue组件中
 */

// 1. API配置
const API_BASE_URL = 'http://localhost:8000/api/v1';

// 2. API客户端类
class FarmlandAPI {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('access_token');
  }

  // 设置认证token
  setToken(token) {
    this.token = token;
    localStorage.setItem('access_token', token);
  }

  // 获取请求头
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    return headers;
  }

  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // 用户认证
  async login(username, password) {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      body: formData,
    });

    if (response.ok) {
      const data = await response.json();
      this.setToken(data.access_token);
      return data;
    } else {
      throw new Error('Login failed');
    }
  }

  // 获取设备GeoJSON数据
  async getEquipmentGeoJSON() {
    return this.request('/frontend/equipment-geojson');
  }

  // 获取设备监测数据
  async getEquipmentMonitoringData(equipmentId) {
    return this.request(`/frontend/equipment-monitoring-data/${equipmentId}`);
  }

  // 获取设备列表
  async getEquipmentList() {
    return this.request('/equipment/');
  }

  // 获取农田监测数据
  async getFarmlandData(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/farmland-data/?${queryString}`);
  }

  // 创建监测数据
  async createFarmlandData(data) {
    return this.request('/farmland-data/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 获取仪表板统计
  async getDashboardStats() {
    return this.request('/frontend/dashboard-stats');
  }
}

// 3. Vue组合式API示例
// 在您的Vue组件中使用：

/*
<script setup>
import { ref, onMounted } from 'vue';

// 创建API实例
const api = new FarmlandAPI();

// 响应式数据
const equipmentList = ref([]);
const monitoringData = ref(null);
const loading = ref(false);

// 获取设备列表
const fetchEquipmentList = async () => {
  try {
    loading.value = true;
    const data = await api.getEquipmentGeoJSON();
    equipmentList.value = data.features;
  } catch (error) {
    console.error('Failed to fetch equipment list:', error);
  } finally {
    loading.value = false;
  }
};

// 获取设备监测数据
const fetchMonitoringData = async (equipmentId) => {
  try {
    const data = await api.getEquipmentMonitoringData(equipmentId);
    monitoringData.value = data;
    return data;
  } catch (error) {
    console.error('Failed to fetch monitoring data:', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchEquipmentList();
});

// 导出供模板使用
defineExpose({
  equipmentList,
  monitoringData,
  loading,
  fetchEquipmentList,
  fetchMonitoringData,
});
</script>
*/

// 4. 替换现有的静态数据获取
// 在您的设备图层组件中，替换这部分代码：

/*
// 原来的代码：
const response1 = await fetch('https://coderfmc.github.io/three.js-demo/监测器_ESPG_4326_WGS84.geojson')
const geoJson1 = await response1.json()

// 替换为：
const api = new FarmlandAPI();
const geoJson1 = await api.getEquipmentGeoJSON();
*/

// 5. 在设备点击事件中获取实时数据
/*
// 原来的静态数据：
const params = {
  show: true,
  pos: movement.position,
  data: new FarmlandMonitoringData({
    soilTemperature: 22,
    soilMoisture: 60,
    // ... 其他静态数据
  })
}

// 替换为动态数据：
const api = new FarmlandAPI();
const realData = await api.getEquipmentMonitoringData(equipmentId);
const params = {
  show: true,
  pos: movement.position,
  data: new FarmlandMonitoringData(realData)
}
*/

// 6. 环境配置
// 在您的vite.config.ts中添加代理配置：
/*
export default defineConfig({
  // ... 其他配置
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      }
    }
  }
})
*/

// 7. 错误处理示例
class APIError extends Error {
  constructor(message, status) {
    super(message);
    this.status = status;
  }
}

// 带错误处理的API调用
async function safeAPICall(apiFunction, ...args) {
  try {
    return await apiFunction(...args);
  } catch (error) {
    if (error.status === 401) {
      // 未授权，重定向到登录页
      window.location.href = '/login';
    } else {
      // 其他错误，显示错误消息
      console.error('API Error:', error.message);
      // 可以使用Element Plus的消息组件显示错误
      // ElMessage.error(error.message);
    }
    throw error;
  }
}

export { FarmlandAPI, APIError, safeAPICall };
