"""
初始化示例数据
"""
import random
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from .database import SessionLocal, create_tables
from .crud.user import user_crud
from .crud.equipment import equipment_crud
from .crud.farmland_data import farmland_data_crud
from .schemas.user import UserCreate
from .schemas.equipment import EquipmentCreate
from .schemas.farmland_data import FarmlandMonitoringDataCreate
from .config import settings


def init_sample_data():
    """初始化示例数据"""
    create_tables()
    db = SessionLocal()
    
    try:
        # 创建管理员用户
        admin_user = user_crud.get_by_username(db, username=settings.admin_username)
        if not admin_user:
            admin_user_in = UserCreate(
                username=settings.admin_username,
                email=settings.admin_email,
                password=settings.admin_password,
                is_admin=True
            )
            admin_user = user_crud.create(db, obj_in=admin_user_in)
            print(f"Created admin user: {admin_user.username}")
        
        # 创建示例设备
        sample_equipment = [
            {
                "name": "农田监测设备1",
                "equipment_id": "FARM_001",
                "longitude": 116.3974,
                "latitude": 39.9093,
                "model": "FM-2000",
                "manufacturer": "农业科技有限公司"
            },
            {
                "name": "农田监测设备2", 
                "equipment_id": "FARM_002",
                "longitude": 116.4074,
                "latitude": 39.9193,
                "model": "FM-2000",
                "manufacturer": "农业科技有限公司"
            },
            {
                "name": "农田监测设备3",
                "equipment_id": "FARM_003", 
                "longitude": 116.3874,
                "latitude": 39.8993,
                "model": "FM-3000",
                "manufacturer": "智慧农业公司"
            }
        ]
        
        equipment_ids = []
        for eq_data in sample_equipment:
            existing_eq = equipment_crud.get_by_equipment_id(db, equipment_id=eq_data["equipment_id"])
            if not existing_eq:
                eq_in = EquipmentCreate(**eq_data)
                equipment = equipment_crud.create(db, obj_in=eq_in)
                equipment_ids.append(equipment.id)
                print(f"Created equipment: {equipment.name}")
            else:
                equipment_ids.append(existing_eq.id)
        
        # 创建示例监测数据
        print("Creating sample monitoring data...")
        for equipment_id in equipment_ids:
            # 为每个设备创建最近7天的数据
            for i in range(7):
                for hour in range(0, 24, 4):  # 每4小时一条数据
                    recorded_time = datetime.now() - timedelta(days=i, hours=hour)
                    
                    # 生成随机但合理的监测数据
                    data_in = FarmlandMonitoringDataCreate(
                        equipment_id=equipment_id,
                        soil_temperature=round(random.uniform(15, 30), 1),
                        soil_moisture=round(random.uniform(30, 80), 1),
                        soil_ph=round(random.uniform(6.0, 8.0), 1),
                        ambient_temperature=round(random.uniform(18, 35), 1),
                        ambient_humidity=round(random.uniform(40, 90), 1),
                        light_intensity=round(random.uniform(10000, 80000), 0),
                        monitor_battery=round(random.uniform(20, 100), 1),
                        recorded_at=recorded_time,
                        notes=f"自动生成的示例数据 - 设备{equipment_id}"
                    )
                    
                    farmland_data_crud.create(db, obj_in=data_in)
        
        print("Sample data initialization completed!")
        
    except Exception as e:
        print(f"Error initializing data: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    init_sample_data()
