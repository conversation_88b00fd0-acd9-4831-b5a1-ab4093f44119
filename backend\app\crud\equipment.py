from typing import List, Optional
from sqlalchemy.orm import Session

from .base import CRUDBase
from ..models.equipment import Equipment
from ..schemas.equipment import EquipmentCreate, EquipmentUpdate


class CRUDEquipment(CRUDBase[Equipment, EquipmentCreate, EquipmentUpdate]):
    """设备CRUD操作"""
    
    def get_by_equipment_id(self, db: Session, *, equipment_id: str) -> Optional[Equipment]:
        """根据设备ID获取设备"""
        return db.query(Equipment).filter(Equipment.equipment_id == equipment_id).first()
    
    def get_active_equipment(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Equipment]:
        """获取活跃设备列表"""
        return db.query(Equipment).filter(Equipment.is_active == True).offset(skip).limit(limit).all()
    
    def get_by_state(self, db: Session, *, state: str, skip: int = 0, limit: int = 100) -> List[Equipment]:
        """根据状态获取设备列表"""
        return db.query(Equipment).filter(Equipment.state == state).offset(skip).limit(limit).all()


equipment_crud = CRUDEquipment(Equipment)
