# 数字农田监测系统

一个完整的数字农田监测系统，包含Vue3+Cesium前端和Python FastAPI后端。

## 🌟 系统特性

### 前端特性
- 🗺️ **3D地图展示** - 基于Cesium的三维地球展示
- 📊 **实时数据可视化** - ECharts图表展示监测数据
- 🎛️ **交互式界面** - Element Plus组件库
- 📱 **响应式设计** - 支持多种设备访问
- 🌐 **多语言支持** - Vue i18n国际化

### 后端特性
- 🚀 **高性能API** - FastAPI异步框架
- 🗄️ **数据库管理** - SQLAlchemy ORM
- 🔐 **安全认证** - JWT Token认证
- 📚 **自动文档** - Swagger/OpenAPI文档
- 🎛️ **管理后台** - Web管理界面
- 🐳 **容器化部署** - Docker支持

## 📁 项目结构

```
digital-farmland/
├── frontend/                 # Vue3前端项目
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── views/          # 页面
│   │   ├── domain/         # 数据模型
│   │   ├── libs/           # Cesium相关
│   │   └── ...
│   ├── package.json
│   └── vite.config.ts
├── backend/                 # Python后端项目
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模型
│   │   ├── crud/           # 数据库操作
│   │   ├── auth/           # 认证模块
│   │   └── admin/          # 管理后台
│   ├── requirements.txt
│   └── run.py
├── start.py                # 启动脚本
├── start.bat              # Windows启动脚本
└── frontend-integration-example.js  # 前端集成示例
```

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd digital-farmland
   ```

2. **运行启动脚本**
   ```bash
   # Linux/macOS
   python start.py
   
   # Windows
   start.bat
   ```

3. **访问系统**
   - 管理后台: http://localhost:8000/admin
   - API文档: http://localhost:8000/docs
   - 前端项目: http://localhost:5173 (需单独启动)

### 方法二：手动启动

#### 启动后端

1. **安装依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **初始化数据库**
   ```bash
   python -m app.init_data
   ```

3. **启动服务**
   ```bash
   python run.py
   ```

#### 启动前端

1. **安装依赖**
   ```bash
   cd frontend  # 您的前端项目目录
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

## 🔧 配置说明

### 后端配置

在 `backend/.env` 文件中配置：

```env
# 数据库配置
DATABASE_URL=sqlite:///./farmland.db

# JWT配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

### 前端配置

在前端项目的 `vite.config.ts` 中添加代理：

```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      }
    }
  }
})
```

## 📊 数据模型

### 设备模型
- 设备ID、名称、位置（经纬度）
- 设备状态、型号、制造商
- 安装日期、活跃状态

### 监测数据模型
- 土壤温度、湿度、pH值
- 环境温度、湿度
- 光照强度、设备电量
- 记录时间、备注信息

## 🔌 API接口

### 认证接口
- `POST /api/v1/auth/login` - 用户登录

### 设备管理
- `GET /api/v1/equipment/` - 获取设备列表
- `POST /api/v1/equipment/` - 创建设备
- `PUT /api/v1/equipment/{id}` - 更新设备
- `DELETE /api/v1/equipment/{id}` - 删除设备

### 监测数据
- `GET /api/v1/farmland-data/` - 获取监测数据
- `POST /api/v1/farmland-data/` - 创建监测数据
- `GET /api/v1/farmland-data/latest/{equipment_id}` - 获取最新数据

### 前端专用接口
- `GET /api/v1/frontend/equipment-geojson` - 获取设备GeoJSON数据
- `GET /api/v1/frontend/equipment-monitoring-data/{id}` - 获取设备监测数据
- `GET /api/v1/frontend/dashboard-stats` - 获取仪表板统计

## 🎯 前端集成

参考 `frontend-integration-example.js` 文件，了解如何在前端项目中集成后端API。

主要步骤：
1. 创建API客户端类
2. 替换静态数据获取
3. 添加认证处理
4. 配置代理转发

## 🐳 Docker部署

```bash
cd backend
docker-compose up -d
```

## 📝 默认账号

- **管理员账号**: admin
- **密码**: admin123

## 🛠️ 开发说明

### 添加新的监测数据类型

1. 在 `backend/app/models/farmland_data.py` 中添加字段
2. 在 `backend/app/schemas/farmland_data.py` 中更新模型
3. 在前端 `src/domain/farmlandMonitoringData.domain.ts` 中添加对应字段
4. 更新管理后台表单

### 添加新的API接口

1. 在 `backend/app/api/` 中创建新的路由文件
2. 在 `backend/app/api/__init__.py` 中注册路由
3. 在前端创建对应的API调用方法

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🆘 常见问题

### Q: 后端启动失败？
A: 检查Python版本（需要3.8+）和依赖安装情况

### Q: 前端无法连接后端？
A: 检查后端是否启动，端口是否正确，代理配置是否正确

### Q: 数据库错误？
A: 删除数据库文件重新初始化：`rm backend/farmland.db && python backend/app/init_data.py`

### Q: 管理后台无法登录？
A: 使用默认账号 admin/admin123，或检查数据库是否正确初始化

## 📞 支持

如有问题，请提交Issue或联系开发团队。
