{% extends "base.html" %}

{% block title %}设备管理 - 数字农田监测系统管理后台{% endblock %}

{% block header %}设备管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h4>监测设备列表</h4>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEquipmentModal">
        <i class="bi bi-plus"></i> 添加设备
    </button>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>设备名称</th>
                        <th>设备编号</th>
                        <th>位置</th>
                        <th>状态</th>
                        <th>型号</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for equipment in equipment_list %}
                    <tr>
                        <td>{{ equipment.id }}</td>
                        <td>{{ equipment.name }}</td>
                        <td>{{ equipment.equipment_id }}</td>
                        <td>{{ "%.4f"|format(equipment.longitude) }}, {{ "%.4f"|format(equipment.latitude) }}</td>
                        <td>
                            {% if equipment.state == "运行中" %}
                                <span class="badge bg-success">{{ equipment.state }}</span>
                            {% elif equipment.state == "离线" %}
                                <span class="badge bg-warning">{{ equipment.state }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ equipment.state }}</span>
                            {% endif %}
                        </td>
                        <td>{{ equipment.model or "-" }}</td>
                        <td>{{ equipment.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editEquipment({{ equipment.id }})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteEquipment({{ equipment.id }})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加设备模态框 -->
<div class="modal fade" id="addEquipmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加监测设备</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addEquipmentForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">设备名称</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="equipment_id" class="form-label">设备编号</label>
                        <input type="text" class="form-control" id="equipment_id" name="equipment_id" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">经度</label>
                                <input type="number" step="0.000001" class="form-control" id="longitude" name="longitude" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">纬度</label>
                                <input type="number" step="0.000001" class="form-control" id="latitude" name="latitude" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="model" class="form-label">设备型号</label>
                        <input type="text" class="form-control" id="model" name="model">
                    </div>
                    <div class="mb-3">
                        <label for="manufacturer" class="form-label">制造商</label>
                        <input type="text" class="form-control" id="manufacturer" name="manufacturer">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitEquipment()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function submitEquipment() {
    const form = document.getElementById('addEquipmentForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 转换数值类型
    data.longitude = parseFloat(data.longitude);
    data.latitude = parseFloat(data.latitude);
    
    fetch('/api/v1/equipment/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            alert('设备添加成功！');
            location.reload();
        } else {
            alert('添加失败：' + (data.detail || '未知错误'));
        }
    })
    .catch(error => {
        alert('添加失败：' + error.message);
    });
}

function editEquipment(id) {
    alert('编辑功能待实现');
}

function deleteEquipment(id) {
    if (confirm('确定要删除这个设备吗？')) {
        fetch(`/api/v1/equipment/${id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                alert('设备删除成功！');
                location.reload();
            } else {
                alert('删除失败');
            }
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    }
}
</script>
{% endblock %}
