from typing import List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from .base import CRUDBase
from ..models.farmland_data import FarmlandMonitoringData
from ..schemas.farmland_data import FarmlandMonitoringDataCreate, FarmlandMonitoringDataUpdate


class CRUDFarmlandData(CRUDBase[FarmlandMonitoringData, FarmlandMonitoringDataCreate, FarmlandMonitoringDataUpdate]):
    """农田监测数据CRUD操作"""
    
    def get_by_equipment(
        self, 
        db: Session, 
        *, 
        equipment_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[FarmlandMonitoringData]:
        """根据设备ID获取监测数据"""
        return (
            db.query(FarmlandMonitoringData)
            .filter(FarmlandMonitoringData.equipment_id == equipment_id)
            .order_by(desc(FarmlandMonitoringData.recorded_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_latest_by_equipment(self, db: Session, *, equipment_id: int) -> Optional[FarmlandMonitoringData]:
        """获取设备最新的监测数据"""
        return (
            db.query(FarmlandMonitoringData)
            .filter(FarmlandMonitoringData.equipment_id == equipment_id)
            .order_by(desc(FarmlandMonitoringData.recorded_at))
            .first()
        )
    
    def get_by_date_range(
        self,
        db: Session,
        *,
        equipment_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[FarmlandMonitoringData]:
        """根据日期范围获取监测数据"""
        query = db.query(FarmlandMonitoringData)
        
        if equipment_id:
            query = query.filter(FarmlandMonitoringData.equipment_id == equipment_id)
        
        if start_date and end_date:
            query = query.filter(
                and_(
                    FarmlandMonitoringData.recorded_at >= start_date,
                    FarmlandMonitoringData.recorded_at <= end_date
                )
            )
        elif start_date:
            query = query.filter(FarmlandMonitoringData.recorded_at >= start_date)
        elif end_date:
            query = query.filter(FarmlandMonitoringData.recorded_at <= end_date)
        
        return (
            query
            .order_by(desc(FarmlandMonitoringData.recorded_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_recent_data(
        self, 
        db: Session, 
        *, 
        hours: int = 24, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[FarmlandMonitoringData]:
        """获取最近几小时的监测数据"""
        since = datetime.utcnow() - timedelta(hours=hours)
        return (
            db.query(FarmlandMonitoringData)
            .filter(FarmlandMonitoringData.recorded_at >= since)
            .order_by(desc(FarmlandMonitoringData.recorded_at))
            .offset(skip)
            .limit(limit)
            .all()
        )


farmland_data_crud = CRUDFarmlandData(FarmlandMonitoringData)
