from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from .equipment import Equipment


class FarmlandMonitoringDataBase(BaseModel):
    """农田监测数据基础模型"""
    equipment_id: int
    soil_temperature: float
    soil_moisture: float
    soil_ph: float
    ambient_temperature: float
    ambient_humidity: float
    light_intensity: float
    monitor_battery: float
    notes: Optional[str] = None


class FarmlandMonitoringDataCreate(FarmlandMonitoringDataBase):
    """创建农田监测数据模型"""
    recorded_at: Optional[datetime] = None


class FarmlandMonitoringDataUpdate(BaseModel):
    """更新农田监测数据模型"""
    soil_temperature: Optional[float] = None
    soil_moisture: Optional[float] = None
    soil_ph: Optional[float] = None
    ambient_temperature: Optional[float] = None
    ambient_humidity: Optional[float] = None
    light_intensity: Optional[float] = None
    monitor_battery: Optional[float] = None
    notes: Optional[str] = None


class FarmlandMonitoringData(FarmlandMonitoringDataBase):
    """返回给客户端的农田监测数据模型"""
    id: int
    recorded_at: datetime
    created_at: datetime
    equipment: Optional[Equipment] = None
    
    class Config:
        from_attributes = True
