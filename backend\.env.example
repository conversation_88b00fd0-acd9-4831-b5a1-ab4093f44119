# 应用配置
APP_NAME=Digital Farmland Backend
APP_VERSION=1.0.0
DEBUG=true

# 数据库配置
DATABASE_URL=sqlite:///./farmland.db
# 生产环境使用PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost/farmland_db

# JWT配置
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"]

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>
