from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session

from ..database import get_db
from ..crud.user import user_crud
from ..crud.equipment import equipment_crud
from ..crud.farmland_data import farmland_data_crud

templates = Jinja2Templates(directory="app/admin/templates")


def setup_admin(app: FastAPI):
    """设置管理后台"""

    @app.get("/admin", response_class=HTMLResponse)
    async def admin_login_page(request: Request):
        """管理后台登录页面"""
        return templates.TemplateResponse("login.html", {"request": request})

    @app.post("/admin/login")
    async def admin_login(
        request: Request,
        username: str = Form(...),
        password: str = Form(...),
        db: Session = Depends(get_db)
    ):
        """管理后台登录处理"""
        user = user_crud.authenticate(db, username=username, password=password)
        if not user or not user.is_admin:
            return templates.TemplateResponse(
                "login.html",
                {"request": request, "error": "用户名或密码错误，或无管理员权限"}
            )

        # 简单的会话管理（生产环境应使用更安全的方式）
        response = RedirectResponse(url="/admin/dashboard", status_code=302)
        response.set_cookie(key="admin_session", value=f"admin_{user.id}")
        return response

    @app.get("/admin/dashboard", response_class=HTMLResponse)
    async def admin_dashboard(request: Request, db: Session = Depends(get_db)):
        """管理后台仪表板"""
        # 简单的会话验证
        session = request.cookies.get("admin_session")
        if not session or not session.startswith("admin_"):
            return RedirectResponse(url="/admin")

        # 获取统计数据
        equipment_count = len(equipment_crud.get_multi(db, limit=1000))
        active_equipment_count = len(equipment_crud.get_active_equipment(db, limit=1000))
        recent_data_count = len(farmland_data_crud.get_recent_data(db, hours=24, limit=1000))

        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "equipment_count": equipment_count,
            "active_equipment_count": active_equipment_count,
            "recent_data_count": recent_data_count
        })

    @app.get("/admin/equipment", response_class=HTMLResponse)
    async def admin_equipment_list(request: Request, db: Session = Depends(get_db)):
        """设备管理页面"""
        session = request.cookies.get("admin_session")
        if not session or not session.startswith("admin_"):
            return RedirectResponse(url="/admin")

        equipment_list = equipment_crud.get_multi(db, limit=100)
        return templates.TemplateResponse("equipment_list.html", {
            "request": request,
            "equipment_list": equipment_list
        })

    @app.get("/admin/farmland-data", response_class=HTMLResponse)
    async def admin_farmland_data_list(request: Request, db: Session = Depends(get_db)):
        """农田数据管理页面"""
        session = request.cookies.get("admin_session")
        if not session or not session.startswith("admin_"):
            return RedirectResponse(url="/admin")

        data_list = farmland_data_crud.get_multi(db, limit=100)
        return templates.TemplateResponse("farmland_data_list.html", {
            "request": request,
            "data_list": data_list
        })

    return app
