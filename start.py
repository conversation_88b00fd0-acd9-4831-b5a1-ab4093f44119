#!/usr/bin/env python3
"""
数字农田监测系统启动脚本
"""
import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def check_dependencies():
    """检查依赖是否安装"""
    backend_dir = Path("backend")
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ 找不到requirements.txt文件")
        return False
    
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        print("✅ 主要依赖已安装")
        return True
    except ImportError:
        print("❌ 缺少必要依赖，正在安装...")
        return install_dependencies()

def install_dependencies():
    """安装依赖"""
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def init_database():
    """初始化数据库"""
    print("🔄 初始化数据库...")
    try:
        # 切换到backend目录
        os.chdir("backend")
        
        # 运行数据库初始化脚本
        subprocess.run([sys.executable, "-m", "app.init_data"], check=True)
        print("✅ 数据库初始化完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 数据库初始化失败")
        return False
    finally:
        # 切换回原目录
        os.chdir("..")

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    try:
        os.chdir("backend")
        # 使用subprocess.Popen启动后端，不等待完成
        process = subprocess.Popen([
            sys.executable, "run.py"
        ])
        print("✅ 后端服务启动中...")
        return process
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None
    finally:
        os.chdir("..")

def wait_for_backend(max_wait=30):
    """等待后端服务启动"""
    import requests
    
    print("⏳ 等待后端服务启动...")
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:8000/health", timeout=1)
            if response.status_code == 200:
                print("✅ 后端服务已就绪")
                return True
        except:
            pass
        time.sleep(1)
        print(f"   等待中... ({i+1}/{max_wait})")
    
    print("❌ 后端服务启动超时")
    return False

def open_browser():
    """打开浏览器"""
    print("🌐 打开浏览器...")
    urls = [
        ("管理后台", "http://localhost:8000/admin"),
        ("API文档", "http://localhost:8000/docs"),
        ("前端项目", "http://localhost:5173")  # Vite默认端口
    ]
    
    for name, url in urls:
        print(f"   {name}: {url}")
    
    # 打开管理后台
    webbrowser.open("http://localhost:8000/admin")

def print_info():
    """打印系统信息"""
    print("\n" + "="*60)
    print("🌾 数字农田监测系统")
    print("="*60)
    print("📊 管理后台: http://localhost:8000/admin")
    print("📚 API文档:  http://localhost:8000/docs")
    print("🔧 API根路径: http://localhost:8000/api/v1")
    print("👤 默认管理员: admin / admin123")
    print("="*60)
    print("💡 提示:")
    print("   - 管理后台可以管理设备和监测数据")
    print("   - API文档提供了完整的接口说明")
    print("   - 前端项目需要单独启动 (npm run dev)")
    print("="*60)

def main():
    """主函数"""
    print("🌾 数字农田监测系统启动器")
    print("-" * 40)
    
    # 检查Python版本
    check_python_version()
    
    # 检查并安装依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，退出")
        sys.exit(1)
    
    # 初始化数据库
    if not init_database():
        print("❌ 数据库初始化失败，退出")
        sys.exit(1)
    
    # 启动后端服务
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端服务启动失败，退出")
        sys.exit(1)
    
    try:
        # 等待后端服务启动
        if wait_for_backend():
            # 打印系统信息
            print_info()
            
            # 打开浏览器
            open_browser()
            
            print("\n🎉 系统启动完成！")
            print("按 Ctrl+C 停止服务")
            
            # 等待用户中断
            backend_process.wait()
        else:
            print("❌ 后端服务启动失败")
            backend_process.terminate()
    
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        backend_process.terminate()
        backend_process.wait()
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
