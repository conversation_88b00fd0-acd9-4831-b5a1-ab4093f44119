from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean
from sqlalchemy.sql import func
from ..database import Base


class Equipment(Base):
    """监测设备模型"""
    __tablename__ = "equipment"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    equipment_id = Column(String(50), unique=True, nullable=False, index=True)
    
    # 位置信息 (经纬度)
    longitude = Column(Float, nullable=False)
    latitude = Column(Float, nullable=False)
    altitude = Column(Float, default=0.0)
    
    # 设备状态
    state = Column(String(20), default="运行中")  # 运行中、离线、故障
    is_active = Column(Boolean, default=True)
    
    # 设备信息
    model = Column(String(100))  # 设备型号
    manufacturer = Column(String(100))  # 制造商
    install_date = Column(DateTime(timezone=True))  # 安装日期
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
